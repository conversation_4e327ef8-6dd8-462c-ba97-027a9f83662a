const { Tail } = require('tail');
const { LogEntry } = require('../database/models');
const moment = require('moment');

let logTail = null;

/**
 * Start monitoring Squid access logs
 */
const startLogMonitoring = () => {
  const accessLogPath = '/var/log/squid/access.log';
  
  try {
    logTail = new Tail(accessLogPath, { follow: true });
    
    logTail.on('line', async (data) => {
      try {
        const logEntry = parseAccessLogLine(data);
        if (logEntry) {
          // Save to database
          await LogEntry.create(logEntry);
          
          // Broadcast to WebSocket clients
          if (global.broadcast) {
            global.broadcast({
              type: 'log_entry',
              data: logEntry,
              timestamp: new Date().toISOString()
            });
          }
        }
      } catch (error) {
        console.error('Error processing log line:', error);
      }
    });

    logTail.on('error', (error) => {
      console.error('Log monitoring error:', error);
    });

    console.log('Log monitoring started for:', accessLogPath);
  } catch (error) {
    console.error('Failed to start log monitoring:', error);
  }
};

/**
 * Stop log monitoring
 */
const stopLogMonitoring = () => {
  if (logTail) {
    logTail.unwatch();
    logTail = null;
    console.log('Log monitoring stopped');
  }
};

/**
 * Parse Squid access log line
 * Format: timestamp elapsed remotehost code/status bytes method URL rfc931 peerstatus/peerhost type
 */
const parseAccessLogLine = (line) => {
  try {
    const parts = line.trim().split(/\s+/);
    
    if (parts.length < 10) {
      return null; // Invalid log line
    }

    const timestamp = parseFloat(parts[0]) * 1000; // Convert to milliseconds
    const elapsed = parseInt(parts[1]);
    const clientIp = parts[2];
    const codeStatus = parts[3].split('/');
    const bytes = parseInt(parts[4]) || 0;
    const method = parts[5];
    const url = parts[6];
    
    // Extract additional fields if available
    let userAgent = '';
    let referer = '';
    
    // Look for quoted strings in the log line (user agent, referer)
    const quotedMatches = line.match(/"([^"]*)"/g);
    if (quotedMatches && quotedMatches.length >= 2) {
      referer = quotedMatches[0].replace(/"/g, '');
      userAgent = quotedMatches[1].replace(/"/g, '');
    }

    return {
      timestamp: new Date(timestamp),
      clientIp: clientIp === '-' ? null : clientIp,
      method: method,
      url: url,
      httpStatus: parseInt(codeStatus[1]) || 0,
      responseSize: bytes,
      userAgent: userAgent || null,
      referer: referer || null,
      processingTime: elapsed
    };
  } catch (error) {
    console.error('Error parsing log line:', error, line);
    return null;
  }
};

/**
 * Parse log line with different formats
 */
const parseLogLineAdvanced = (line) => {
  // Common Log Format (CLF)
  const clfRegex = /^(\S+) \S+ \S+ \[([^\]]+)\] "(\S+) (\S+) (\S+)" (\d+) (\d+|-)/;
  
  // Combined Log Format
  const combinedRegex = /^(\S+) \S+ \S+ \[([^\]]+)\] "(\S+) (\S+) (\S+)" (\d+) (\d+|-) "([^"]*)" "([^"]*)"/;
  
  // Squid Native Format
  const squidRegex = /^(\d+\.\d+)\s+(\d+)\s+(\S+)\s+(\S+)\/(\d+)\s+(\d+)\s+(\S+)\s+(\S+)/;
  
  let match;
  
  // Try Squid native format first
  if ((match = line.match(squidRegex))) {
    return {
      timestamp: new Date(parseFloat(match[1]) * 1000),
      processingTime: parseInt(match[2]),
      clientIp: match[3],
      httpStatus: parseInt(match[5]),
      responseSize: parseInt(match[6]),
      method: match[7],
      url: match[8],
      userAgent: null,
      referer: null
    };
  }
  
  // Try Combined Log Format
  if ((match = line.match(combinedRegex))) {
    return {
      timestamp: moment(match[2], 'DD/MMM/YYYY:HH:mm:ss ZZ').toDate(),
      clientIp: match[1],
      method: match[3],
      url: match[4],
      httpStatus: parseInt(match[6]),
      responseSize: match[7] === '-' ? 0 : parseInt(match[7]),
      referer: match[8],
      userAgent: match[9],
      processingTime: 0
    };
  }
  
  // Try Common Log Format
  if ((match = line.match(clfRegex))) {
    return {
      timestamp: moment(match[2], 'DD/MMM/YYYY:HH:mm:ss ZZ').toDate(),
      clientIp: match[1],
      method: match[3],
      url: match[4],
      httpStatus: parseInt(match[6]),
      responseSize: match[7] === '-' ? 0 : parseInt(match[7]),
      referer: null,
      userAgent: null,
      processingTime: 0
    };
  }
  
  return null;
};

/**
 * Get real-time statistics
 */
const getRealTimeStats = async () => {
  try {
    const { Op } = require('sequelize');
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    const recentStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: fiveMinutesAgo
        }
      },
      attributes: [
        [require('sequelize').fn('COUNT', '*'), 'requestCount'],
        [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('clientIp'))), 'uniqueClients'],
        [require('sequelize').fn('SUM', require('sequelize').col('responseSize')), 'totalBytes'],
        [require('sequelize').fn('AVG', require('sequelize').col('processingTime')), 'avgResponseTime']
      ],
      raw: true
    });

    return recentStats[0] || {
      requestCount: 0,
      uniqueClients: 0,
      totalBytes: 0,
      avgResponseTime: 0
    };
  } catch (error) {
    console.error('Error getting real-time stats:', error);
    return {
      requestCount: 0,
      uniqueClients: 0,
      totalBytes: 0,
      avgResponseTime: 0
    };
  }
};

// Broadcast real-time stats every 30 seconds
setInterval(async () => {
  if (global.broadcast) {
    const stats = await getRealTimeStats();
    global.broadcast({
      type: 'realtime_stats',
      data: stats,
      timestamp: new Date().toISOString()
    });
  }
}, 30000);

module.exports = {
  startLogMonitoring,
  stopLogMonitoring,
  parseAccessLogLine,
  getRealTimeStats
};
