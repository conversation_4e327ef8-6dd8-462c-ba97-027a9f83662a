version: '3.8'

services:
  # Squid Proxy Server
  squid:
    build:
      context: ./docker/squid
      dockerfile: Dockerfile
    container_name: squid-proxy
    restart: unless-stopped
    ports:
      - "${SQUID_PORT:-3128}:3128"
    volumes:
      - ./config/squid:/etc/squid:ro
      - squid_cache:/var/spool/squid
      - squid_logs:/var/log/squid
      - ./data/squid:/data
    environment:
      - SQUID_USER=proxy
      - SQUID_CACHE_DIR=/var/spool/squid
      - SQUID_LOG_DIR=/var/log/squid
      - SQUID_CONFIG_DIR=/etc/squid
      - SQUID_AUTH_USER=${ADMIN_USERNAME:-admin}
      - SQUID_AUTH_PASS=${ADMIN_PASSWORD:-admin123}
    networks:
      - squid-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # API Backend
  api:
    build:
      context: ./src/api
      dockerfile: ../../docker/api/Dockerfile
    container_name: squid-api
    restart: unless-stopped
    ports:
      - "${API_PORT:-3000}:3000"
    volumes:
      - squid_logs:/var/log/squid:ro
      - ./config/squid:/etc/squid:ro
      - ./data/database:/app/data
      - ./data/uploads:/app/uploads
    environment:
      - NODE_ENV=production
      - API_PORT=3000
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-change-in-production}
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:8080}
      - DB_PATH=/app/data/squid_proxy.db
    depends_on:
      - squid
    networks:
      - squid-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Frontend Web UI
  frontend:
    build:
      context: ./src/frontend
      dockerfile: ../../docker/frontend/Dockerfile
    container_name: squid-frontend
    restart: unless-stopped
    ports:
      - "${WEB_UI_PORT:-8080}:8080"
    depends_on:
      - api
    networks:
      - squid-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optional: Database for advanced features
  database:
    image: postgres:15-alpine
    container_name: squid-database
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME:-squid_proxy}
      - POSTGRES_USER=${DB_USER:-squid_user}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-change_me_in_production}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - squid-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-squid_user} -d ${DB_NAME:-squid_proxy}"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - postgres

  # Optional: Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: squid-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    networks:
      - squid-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - redis

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: squid-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus:/etc/prometheus:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - squid-network
    profiles:
      - monitoring

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: squid-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - squid-network
    profiles:
      - monitoring

networks:
  squid-network:
    driver: bridge
    ipam:
      config:
        - subnet: ${NETWORK_SUBNET:-**********/16}

volumes:
  squid_cache:
    driver: local
  squid_logs:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
