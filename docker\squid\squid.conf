# Squid Proxy Server Configuration
# Production-ready configuration with security, performance, and monitoring

# Network Configuration
http_port 3128
# Optional: HTTPS proxy port
# https_port 3129 cert=/etc/squid/ssl/squid.pem

# Cache Configuration
cache_dir ufs /var/spool/squid 1024 16 256
cache_mem 256 MB
maximum_object_size 100 MB
maximum_object_size_in_memory 8 MB

# Cache replacement policy
cache_replacement_policy lru

# Memory pools
memory_pools on
memory_pools_limit 64 MB

# Access Control Lists (ACLs)
acl localnet src 10.0.0.0/8
acl localnet src 172.16.0.0/12
acl localnet src 192.168.0.0/16
acl localnet src fc00::/7
acl localnet src fe80::/10

# Safe ports
acl SSL_ports port 443
acl Safe_ports port 80
acl Safe_ports port 21
acl Safe_ports port 443
acl Safe_ports port 70
acl Safe_ports port 210
acl Safe_ports port 1025-65535
acl Safe_ports port 280
acl Safe_ports port 488
acl Safe_ports port 591
acl Safe_ports port 777

# HTTP methods
acl CONNECT method CONNECT
acl GET method GET
acl POST method POST
acl HEAD method HEAD

# Time-based ACLs
acl business_hours time MTWHF 09:00-18:00

# Authentication (uncomment to enable)
# auth_param basic program /usr/lib/squid/basic_ncsa_auth /etc/squid/passwd
# auth_param basic children 5 startup=5 idle=1
# auth_param basic realm Squid Proxy Server
# auth_param basic credentialsttl 2 hours
# acl authenticated proxy_auth REQUIRED

# Access Rules
# Deny requests to certain unsafe ports
http_access deny !Safe_ports

# Deny CONNECT to other than secure SSL ports
http_access deny CONNECT !SSL_ports

# Only allow cachemgr access from localhost
http_access allow localhost manager
http_access deny manager

# Allow local network access
http_access allow localnet
http_access allow localhost

# Authentication required (uncomment if using auth)
# http_access allow authenticated

# Default deny
http_access deny all

# Logging Configuration
access_log /var/log/squid/access.log squid
cache_log /var/log/squid/cache.log
cache_store_log /var/log/squid/store.log

# Log format for detailed monitoring
logformat combined %>a %[ui %[un [%tl] "%rm %ru HTTP/%rv" %>Hs %<st "%{Referer}>h" "%{User-Agent}>h" %Ss:%Sh

# Performance Tuning
# DNS settings
dns_nameservers ******* ******* *******
dns_timeout 30 seconds

# Connection settings
client_lifetime 1 day
half_closed_clients off
pconn_timeout 60 seconds
request_timeout 5 minutes

# Cache tuning
quick_abort_min 16 KB
quick_abort_max 16 KB
quick_abort_pct 95

# Refresh patterns for better caching
refresh_pattern ^ftp: 1440 20% 10080
refresh_pattern ^gopher: 1440 0% 1440
refresh_pattern -i (/cgi-bin/|\?) 0 0% 0
refresh_pattern . 0 20% 4320

# Security Headers
reply_header_add X-Proxy-Server "Squid Proxy"
reply_header_add X-Cache-Status "%{Squid-Status}>h"

# Miscellaneous
coredump_dir /var/spool/squid
leave_err_details on
strip_query_terms off

# Shutdown timeout
shutdown_lifetime 10 seconds

# Process management
cache_effective_user proxy
cache_effective_group proxy

# Disable via header for security
via off
forwarded_for delete

# Error pages customization
error_directory /usr/share/squid/errors/English
