@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply text-gray-900 bg-gray-50;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 border-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-white border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 border-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-success {
    @apply btn bg-green-600 border-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }
  
  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .stat-card {
    @apply card p-6;
  }
  
  .stat-value {
    @apply text-2xl font-bold text-gray-900;
  }
  
  .stat-label {
    @apply text-sm font-medium text-gray-500;
  }
  
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700;
  }
  
  .nav-link-inactive {
    @apply nav-link text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Monaco Editor theme adjustments */
.monaco-editor {
  @apply rounded-lg;
}

/* React Table styles */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table thead {
  @apply bg-gray-50;
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:nth-child(even) {
  @apply bg-gray-50;
}

.table tbody tr:hover {
  @apply bg-gray-100;
}

/* Chart tooltips */
.recharts-tooltip-wrapper {
  @apply rounded-lg shadow-lg;
}

.recharts-default-tooltip {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
}

/* Form validation */
.form-error {
  @apply text-red-600 text-sm mt-1;
}

.form-group {
  @apply mb-4;
}

/* Status indicators */
.status-dot {
  @apply inline-block w-2 h-2 rounded-full mr-2;
}

.status-running {
  @apply status-dot bg-green-500;
}

.status-stopped {
  @apply status-dot bg-red-500;
}

.status-warning {
  @apply status-dot bg-yellow-500;
}

.status-unknown {
  @apply status-dot bg-gray-400;
}
