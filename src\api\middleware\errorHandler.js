/**
 * Global error handler middleware
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // Default error
  let error = {
    status: 500,
    message: 'Internal Server Error'
  };

  // Handle specific error types
  if (err.name === 'ValidationError') {
    error = {
      status: 400,
      message: 'Validation Error',
      details: err.details || err.message
    };
  } else if (err.name === 'UnauthorizedError') {
    error = {
      status: 401,
      message: 'Unauthorized'
    };
  } else if (err.name === 'ForbiddenError') {
    error = {
      status: 403,
      message: 'Forbidden'
    };
  } else if (err.name === 'NotFoundError') {
    error = {
      status: 404,
      message: 'Not Found'
    };
  } else if (err.name === 'ConflictError') {
    error = {
      status: 409,
      message: 'Conflict'
    };
  } else if (err.code === 'ENOENT') {
    error = {
      status: 404,
      message: 'File not found'
    };
  } else if (err.code === 'EACCES') {
    error = {
      status: 403,
      message: 'Permission denied'
    };
  } else if (err.code === 'ECONNREFUSED') {
    error = {
      status: 503,
      message: 'Service unavailable'
    };
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && error.status === 500) {
    error.message = 'Internal Server Error';
    delete error.details;
  } else if (process.env.NODE_ENV !== 'production') {
    // Include stack trace in development
    error.stack = err.stack;
  }

  res.status(error.status).json({
    error: error.message,
    ...(error.details && { details: error.details }),
    ...(error.stack && { stack: error.stack }),
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method
  });
};

/**
 * Async error wrapper
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Custom error classes
 */
class ValidationError extends Error {
  constructor(message, details) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

class UnauthorizedError extends Error {
  constructor(message = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

class ForbiddenError extends Error {
  constructor(message = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

class NotFoundError extends Error {
  constructor(message = 'Not Found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

class ConflictError extends Error {
  constructor(message = 'Conflict') {
    super(message);
    this.name = 'ConflictError';
  }
}

module.exports = {
  errorHandler,
  asyncHandler,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError
};
