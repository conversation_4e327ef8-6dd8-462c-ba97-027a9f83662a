# MIME Type Configuration for Squid Proxy
# This file defines MIME types and their handling

# Common MIME types for caching optimization
acl html_content rep_mime_type text/html
acl css_content rep_mime_type text/css
acl js_content rep_mime_type application/javascript
acl js_content rep_mime_type text/javascript
acl image_content rep_mime_type image/jpeg
acl image_content rep_mime_type image/png
acl image_content rep_mime_type image/gif
acl image_content rep_mime_type image/webp
acl video_content rep_mime_type video/mp4
acl video_content rep_mime_type video/webm
acl audio_content rep_mime_type audio/mpeg
acl audio_content rep_mime_type audio/wav
acl pdf_content rep_mime_type application/pdf
acl zip_content rep_mime_type application/zip
acl exe_content rep_mime_type application/octet-stream

# Cache control based on MIME types
# Cache static content longer
cache_dir_options max-size=104857600 min-size=0

# Refresh patterns based on content type
refresh_pattern -i \.(jpg|jpeg|png|gif|webp|ico)$ 10080 90% 43200 override-expire ignore-no-cache ignore-no-store ignore-private
refresh_pattern -i \.(css|js)$ 1440 40% 40320 override-expire
refresh_pattern -i \.(pdf|zip|exe|dmg|pkg)$ 10080 90% 43200 override-expire
refresh_pattern -i \.(mp4|webm|avi|mov|wmv)$ 10080 90% 43200 override-expire
refresh_pattern -i \.(mp3|wav|ogg|flac)$ 10080 90% 43200 override-expire

# Don't cache dynamic content
acl dynamic_content urlpath_regex -i \.(php|asp|aspx|jsp|cgi|pl)$
cache deny dynamic_content

# Don't cache private or sensitive content
acl private_content rep_mime_type application/json
acl private_content rep_mime_type application/xml
acl private_content rep_mime_type text/xml
cache deny private_content

# Security: Block potentially dangerous MIME types
acl dangerous_content rep_mime_type application/x-msdownload
acl dangerous_content rep_mime_type application/x-msdos-program
acl dangerous_content rep_mime_type application/x-executable
http_reply_access deny dangerous_content

# Compression settings for different MIME types
# Enable compression for text-based content
acl compressible_content rep_mime_type text/html
acl compressible_content rep_mime_type text/css
acl compressible_content rep_mime_type text/javascript
acl compressible_content rep_mime_type application/javascript
acl compressible_content rep_mime_type application/json
acl compressible_content rep_mime_type application/xml
acl compressible_content rep_mime_type text/xml

# Don't compress already compressed content
acl compressed_content rep_mime_type image/jpeg
acl compressed_content rep_mime_type image/png
acl compressed_content rep_mime_type image/gif
acl compressed_content rep_mime_type video/mp4
acl compressed_content rep_mime_type audio/mpeg
acl compressed_content rep_mime_type application/zip
acl compressed_content rep_mime_type application/gzip
