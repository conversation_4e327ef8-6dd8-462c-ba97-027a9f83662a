# Squid Proxy Server Configuration
# Production-ready configuration with security, performance, and monitoring
# This is the main configuration file mounted into the container

# Network Configuration
http_port 3128

# Cache Configuration
cache_dir ufs /var/spool/squid 1024 16 256
cache_mem 256 MB
maximum_object_size 100 MB
maximum_object_size_in_memory 8 MB
cache_replacement_policy lru

# Memory settings
memory_pools on
memory_pools_limit 64 MB

# Access Control Lists (ACLs)
acl localnet src 10.0.0.0/8
acl localnet src 172.16.0.0/12
acl localnet src 192.168.0.0/16
acl localnet src fc00::/7
acl localnet src fe80::/10

# Safe ports
acl SSL_ports port 443
acl Safe_ports port 80
acl Safe_ports port 21
acl Safe_ports port 443
acl Safe_ports port 70
acl Safe_ports port 210
acl Safe_ports port 1025-65535
acl Safe_ports port 280
acl Safe_ports port 488
acl Safe_ports port 591
acl Safe_ports port 777

# HTTP methods
acl CONNECT method CONNECT

# Time-based ACLs
acl business_hours time MTWHF 09:00-18:00

# Access Rules
http_access deny !Safe_ports
http_access deny CONNECT !SSL_ports
http_access allow localhost manager
http_access deny manager
http_access allow localnet
http_access allow localhost
http_access deny all

# Logging Configuration
access_log /var/log/squid/access.log squid
cache_log /var/log/squid/cache.log
cache_store_log /var/log/squid/store.log

# Performance Tuning
dns_nameservers ******* ******* *******
dns_timeout 30 seconds
client_lifetime 1 day
half_closed_clients off
pconn_timeout 60 seconds
request_timeout 5 minutes

# Cache tuning
quick_abort_min 16 KB
quick_abort_max 16 KB
quick_abort_pct 95

# Refresh patterns
refresh_pattern ^ftp: 1440 20% 10080
refresh_pattern ^gopher: 1440 0% 1440
refresh_pattern -i (/cgi-bin/|\?) 0 0% 0
refresh_pattern -i \.(jpg|jpeg|png|gif|webp|ico)$ 10080 90% 43200 override-expire ignore-no-cache
refresh_pattern -i \.(css|js)$ 1440 40% 40320 override-expire
refresh_pattern -i \.(pdf|zip|exe)$ 10080 90% 43200 override-expire
refresh_pattern . 0 20% 4320

# Security settings
via off
forwarded_for delete
reply_header_add X-Proxy-Server "Squid Proxy"

# Process settings
cache_effective_user proxy
cache_effective_group proxy
coredump_dir /var/spool/squid
shutdown_lifetime 10 seconds

# Error pages
error_directory /usr/share/squid/errors/English
