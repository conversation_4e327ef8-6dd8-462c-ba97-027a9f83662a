#!/bin/bash

# Squid Health Check Script
set -e

# Configuration
SQUID_PORT=${SQUID_PORT:-3128}
TIMEOUT=5

# Function to log messages
log() {
    echo "[HEALTHCHECK] $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# Check if Squid process is running
if ! pgrep -x squid > /dev/null; then
    log "ERROR: Squid process not running"
    exit 1
fi

# Check if <PERSON><PERSON> is listening on the configured port
if ! netstat -ln | grep -q ":$SQUID_PORT "; then
    log "ERROR: Squid not listening on port $SQUID_PORT"
    exit 1
fi

# Test proxy functionality with a simple HTTP request
if ! curl -x localhost:$SQUID_PORT --connect-timeout $TIMEOUT -s -o /dev/null http://www.google.com; then
    log "ERROR: Proxy functionality test failed"
    exit 1
fi

# Check cache directory permissions
SQUID_CACHE_DIR=${SQUID_CACHE_DIR:-/var/spool/squid}
if [ ! -w "$SQUID_CACHE_DIR" ]; then
    log "ERROR: Cache directory not writable"
    exit 1
fi

# Check log directory permissions
SQUID_LOG_DIR=${SQUID_LOG_DIR:-/var/log/squid}
if [ ! -w "$SQUID_LOG_DIR" ]; then
    log "ERROR: Log directory not writable"
    exit 1
fi

log "Health check passed - Squid is healthy"
exit 0
