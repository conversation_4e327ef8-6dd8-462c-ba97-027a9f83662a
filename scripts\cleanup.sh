#!/bin/bash

# Squid Proxy Solution - Cleanup Script
set -e

echo "🧹 Cleaning up Squid Proxy Solution..."
echo "⚠️  This will remove all containers, volumes, and data!"
echo ""

read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Cleanup cancelled."
    exit 1
fi

# Stop and remove containers
echo "🛑 Stopping and removing containers..."
if command -v docker-compose &> /dev/null; then
    docker-compose down -v --remove-orphans
else
    docker compose down -v --remove-orphans
fi

# Remove images
echo "🗑️  Removing images..."
docker images | grep -E "(squid|squid-proxy)" | awk '{print $3}' | xargs -r docker rmi -f

# Remove data directories
echo "📁 Removing data directories..."
rm -rf data/

# Remove .env file
if [ -f .env ]; then
    read -p "Remove .env file? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm .env
        echo "✅ .env file removed"
    fi
fi

echo ""
echo "✅ Cleanup completed successfully!"
echo "💡 To start fresh, run: ./scripts/start.sh"
