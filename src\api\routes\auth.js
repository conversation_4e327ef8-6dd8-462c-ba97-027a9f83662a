const express = require('express');
const { body, validationResult } = require('express-validator');
const { 
  generateToken, 
  hashPassword, 
  comparePassword, 
  authenticateToken,
  authRateLimit 
} = require('../middleware/auth');
const { asyncHandler, ValidationError, UnauthorizedError } = require('../middleware/errorHandler');
const { User } = require('../database/models');

const router = express.Router();

/**
 * Login endpoint
 */
router.post('/login', 
  authRateLimit,
  [
    body('username').notEmpty().withMessage('Username is required'),
    body('password').notEmpty().withMessage('Password is required')
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('Validation failed', errors.array());
    }

    const { username, password } = req.body;

    // Find user
    const user = await User.findOne({ where: { username } });
    if (!user) {
      throw new UnauthorizedError('Invalid credentials');
    }

    // Check password
    const isValidPassword = await comparePassword(password, user.password);
    if (!isValidPassword) {
      throw new UnauthorizedError('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedError('Account is disabled');
    }

    // Generate token
    const token = generateToken({
      id: user.id,
      username: user.username,
      role: user.role
    });

    // Update last login
    await user.update({ lastLogin: new Date() });

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        email: user.email,
        lastLogin: user.lastLogin
      }
    });
  })
);

/**
 * Register endpoint (admin only)
 */
router.post('/register',
  authenticateToken,
  [
    body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('role').isIn(['admin', 'user']).withMessage('Role must be admin or user')
  ],
  asyncHandler(async (req, res) => {
    // Only admin can create users
    if (req.user.role !== 'admin') {
      throw new UnauthorizedError('Admin privileges required');
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('Validation failed', errors.array());
    }

    const { username, password, email, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ 
      where: { 
        $or: [{ username }, { email }] 
      } 
    });
    
    if (existingUser) {
      throw new ValidationError('User already exists');
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await User.create({
      username,
      password: hashedPassword,
      email,
      role,
      isActive: true
    });

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        isActive: user.isActive
      }
    });
  })
);

/**
 * Get current user profile
 */
router.get('/profile', authenticateToken, asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    attributes: { exclude: ['password'] }
  });

  if (!user) {
    throw new UnauthorizedError('User not found');
  }

  res.json({
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin
    }
  });
}));

/**
 * Update user profile
 */
router.put('/profile',
  authenticateToken,
  [
    body('email').optional().isEmail().withMessage('Valid email is required'),
    body('currentPassword').optional().notEmpty().withMessage('Current password is required'),
    body('newPassword').optional().isLength({ min: 6 }).withMessage('New password must be at least 6 characters')
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('Validation failed', errors.array());
    }

    const { email, currentPassword, newPassword } = req.body;
    const user = await User.findByPk(req.user.id);

    if (!user) {
      throw new UnauthorizedError('User not found');
    }

    const updates = {};

    // Update email if provided
    if (email && email !== user.email) {
      // Check if email is already taken
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser && existingUser.id !== user.id) {
        throw new ValidationError('Email already in use');
      }
      updates.email = email;
    }

    // Update password if provided
    if (newPassword) {
      if (!currentPassword) {
        throw new ValidationError('Current password is required to change password');
      }

      const isValidPassword = await comparePassword(currentPassword, user.password);
      if (!isValidPassword) {
        throw new UnauthorizedError('Current password is incorrect');
      }

      updates.password = await hashPassword(newPassword);
    }

    // Update user
    if (Object.keys(updates).length > 0) {
      await user.update(updates);
    }

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  })
);

/**
 * Logout endpoint (client-side token invalidation)
 */
router.post('/logout', authenticateToken, (req, res) => {
  res.json({
    message: 'Logout successful'
  });
});

/**
 * Verify token endpoint
 */
router.get('/verify', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    }
  });
});

module.exports = router;
