const express = require('express');
const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs-extra');
const { asyncHandler, NotFoundError, ValidationError } = require('../middleware/errorHandler');

const router = express.Router();
const execAsync = promisify(exec);

// Squid service management commands
const SQUID_COMMANDS = {
  status: 'supervisorctl status squid',
  start: 'supervisorctl start squid',
  stop: 'supervisorctl stop squid',
  restart: 'supervisorctl restart squid',
  reload: 'squid -k reconfigure'
};

/**
 * Get proxy status
 */
router.get('/status', asyncHandler(async (req, res) => {
  try {
    const { stdout } = await execAsync(SQUID_COMMANDS.status);
    const isRunning = stdout.includes('RUNNING');
    
    // Get additional status information
    let processInfo = null;
    if (isRunning) {
      try {
        const { stdout: psOutput } = await execAsync('ps aux | grep squid | grep -v grep');
        processInfo = psOutput.trim().split('\n').map(line => {
          const parts = line.trim().split(/\s+/);
          return {
            pid: parts[1],
            cpu: parts[2],
            memory: parts[3],
            command: parts.slice(10).join(' ')
          };
        });
      } catch (error) {
        console.warn('Could not get process info:', error.message);
      }
    }

    res.json({
      status: isRunning ? 'running' : 'stopped',
      uptime: isRunning ? await getSquidUptime() : null,
      processInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting proxy status:', error);
    res.json({
      status: 'unknown',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Start proxy service
 */
router.post('/start', asyncHandler(async (req, res) => {
  try {
    const { stdout, stderr } = await execAsync(SQUID_COMMANDS.start);
    
    // Broadcast status change
    if (global.broadcast) {
      global.broadcast({
        type: 'proxy_status',
        status: 'starting',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      message: 'Proxy service start command executed',
      output: stdout,
      error: stderr,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    throw new ValidationError('Failed to start proxy service', error.message);
  }
}));

/**
 * Stop proxy service
 */
router.post('/stop', asyncHandler(async (req, res) => {
  try {
    const { stdout, stderr } = await execAsync(SQUID_COMMANDS.stop);
    
    // Broadcast status change
    if (global.broadcast) {
      global.broadcast({
        type: 'proxy_status',
        status: 'stopping',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      message: 'Proxy service stop command executed',
      output: stdout,
      error: stderr,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    throw new ValidationError('Failed to stop proxy service', error.message);
  }
}));

/**
 * Restart proxy service
 */
router.post('/restart', asyncHandler(async (req, res) => {
  try {
    const { stdout, stderr } = await execAsync(SQUID_COMMANDS.restart);
    
    // Broadcast status change
    if (global.broadcast) {
      global.broadcast({
        type: 'proxy_status',
        status: 'restarting',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      message: 'Proxy service restart command executed',
      output: stdout,
      error: stderr,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    throw new ValidationError('Failed to restart proxy service', error.message);
  }
}));

/**
 * Reload proxy configuration
 */
router.post('/reload', asyncHandler(async (req, res) => {
  try {
    const { stdout, stderr } = await execAsync(SQUID_COMMANDS.reload);
    
    // Broadcast configuration reload
    if (global.broadcast) {
      global.broadcast({
        type: 'config_reload',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      message: 'Proxy configuration reloaded',
      output: stdout,
      error: stderr,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    throw new ValidationError('Failed to reload proxy configuration', error.message);
  }
}));

/**
 * Get proxy statistics
 */
router.get('/stats', asyncHandler(async (req, res) => {
  try {
    // Get cache manager statistics
    const stats = await getSquidStats();
    
    res.json({
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting proxy stats:', error);
    res.json({
      stats: null,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Helper function to get Squid uptime
 */
async function getSquidUptime() {
  try {
    const { stdout } = await execAsync('ps -o etime= -p $(pgrep squid | head -1)');
    return stdout.trim();
  } catch (error) {
    return null;
  }
}

/**
 * Helper function to get Squid statistics
 */
async function getSquidStats() {
  try {
    // Try to get cache manager info
    const { stdout } = await execAsync('squidclient -h localhost -p 3128 mgr:info');
    
    const stats = {
      cacheInfo: stdout,
      connections: await getConnectionStats(),
      bandwidth: await getBandwidthStats()
    };
    
    return stats;
  } catch (error) {
    console.warn('Could not get detailed stats:', error.message);
    return {
      error: 'Statistics not available',
      connections: await getConnectionStats(),
      bandwidth: null
    };
  }
}

/**
 * Helper function to get connection statistics
 */
async function getConnectionStats() {
  try {
    const { stdout } = await execAsync('netstat -an | grep :3128 | wc -l');
    return {
      activeConnections: parseInt(stdout.trim()) || 0
    };
  } catch (error) {
    return { activeConnections: 0 };
  }
}

/**
 * Helper function to get bandwidth statistics
 */
async function getBandwidthStats() {
  try {
    // This would require more sophisticated monitoring
    // For now, return placeholder data
    return {
      bytesIn: 0,
      bytesOut: 0,
      requestsPerSecond: 0
    };
  } catch (error) {
    return null;
  }
}

module.exports = router;
