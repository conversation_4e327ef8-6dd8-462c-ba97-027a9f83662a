# Squid Proxy Server Dockerfile
FROM ubuntu:24.04

# Metadata
LABEL maintainer="Squid Proxy Solution"
LABEL description="Production-ready Squid proxy server with security and performance optimizations"
LABEL version="1.0"

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV SQUID_VERSION=6.6
ENV SQUID_USER=proxy
ENV SQUID_CACHE_DIR=/var/spool/squid
ENV SQUID_LOG_DIR=/var/log/squid
ENV SQUID_CONFIG_DIR=/etc/squid

# Install dependencies and Squid
RUN apt-get update && \
    apt-get install -y \
        squid \
        squid-common \
        squid-langpack \
        apache2-utils \
        curl \
        wget \
        nano \
        logrotate \
        supervisor \
        cron \
        rsyslog \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create squid user and directories
RUN useradd -r -s /bin/false $SQUID_USER && \
    mkdir -p $SQUID_CACHE_DIR $SQUID_LOG_DIR && \
    chown -R $SQUID_USER:$SQUID_USER $SQUID_CACHE_DIR $SQUID_LOG_DIR && \
    chmod 755 $SQUID_CACHE_DIR $SQUID_LOG_DIR

# Copy configuration files
COPY squid.conf $SQUID_CONFIG_DIR/squid.conf
COPY squid-auth.conf $SQUID_CONFIG_DIR/squid-auth.conf
COPY mime.conf $SQUID_CONFIG_DIR/mime.conf

# Copy startup scripts
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
COPY healthcheck.sh /usr/local/bin/healthcheck.sh
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set permissions
RUN chmod +x /usr/local/bin/entrypoint.sh /usr/local/bin/healthcheck.sh && \
    chown root:root $SQUID_CONFIG_DIR/squid.conf && \
    chmod 644 $SQUID_CONFIG_DIR/squid.conf

# Create log rotation configuration
RUN echo "$SQUID_LOG_DIR/access.log {\n\
    daily\n\
    missingok\n\
    rotate 30\n\
    compress\n\
    delaycompress\n\
    notifempty\n\
    postrotate\n\
        /usr/bin/killall -USR1 squid\n\
    endscript\n\
}" > /etc/logrotate.d/squid

# Initialize Squid cache directories
RUN squid -z -f $SQUID_CONFIG_DIR/squid.conf

# Expose ports
EXPOSE 3128 3129

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Set working directory
WORKDIR /etc/squid

# Use supervisor to manage processes
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
