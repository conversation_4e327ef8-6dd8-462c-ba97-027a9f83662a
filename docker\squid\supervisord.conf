[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[program:squid]
command=/usr/sbin/squid -N -f /etc/squid/squid.conf
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/squid/supervisor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
priority=10

[program:rsyslog]
command=/usr/sbin/rsyslogd -n
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/rsyslog.log
priority=5

[program:cron]
command=/usr/sbin/cron -f
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/cron.log
priority=5
