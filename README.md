# Squid Proxy Server Solution

A complete, production-ready Squid proxy server solution with Docker containerization and modern web UI management interface.

## Features

- **Dockerized Squid Proxy**: Ubuntu 24.04 LTS based container with optimized Squid configuration
- **Web Management Interface**: Modern React-based UI for proxy management
- **Real-time Monitoring**: Live statistics, connection monitoring, and bandwidth usage
- **Configuration Management**: Web-based configuration of proxy settings, ACLs, and cache
- **Log Management**: Comprehensive logging with web-based log viewer and search
- **Authentication**: Built-in user authentication and access control
- **Security**: Proper ACLs, security headers, and access restrictions
- **Performance**: Optimized caching and performance tuning

## Project Structure

```
squid/
├── docker/
│   ├── squid/                 # Squid proxy container
│   ├── api/                   # Backend API container
│   └── frontend/              # Frontend web UI container
├── config/
│   ├── squid/                 # Squid configuration files
│   └── nginx/                 # Nginx configuration (if needed)
├── src/
│   ├── api/                   # Backend API source code
│   └── frontend/              # Frontend React application
├── docs/                      # Documentation
├── scripts/                   # Utility scripts
├── docker-compose.yml         # Docker Compose configuration
└── .env.example              # Environment variables template
```

## Quick Start

1. Clone the repository
2. Copy `.env.example` to `.env` and configure
3. Run `docker-compose up -d`
4. Access the web UI at `http://localhost:8080`
5. Configure your proxy settings at `http://localhost:3128`

## Documentation

- [Setup Guide](docs/setup.md)
- [Configuration Reference](docs/configuration.md)
- [API Documentation](docs/api.md)
- [Troubleshooting](docs/troubleshooting.md)

## License

MIT License - see LICENSE file for details.
