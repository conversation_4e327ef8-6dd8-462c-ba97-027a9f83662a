const express = require('express');
const { body, validationResult } = require('express-validator');
const fs = require('fs-extra');
const path = require('path');
const { asyncHandler, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Configuration file paths
const CONFIG_PATHS = {
  squid: '/etc/squid/squid.conf',
  auth: '/etc/squid/squid-auth.conf',
  mime: '/etc/squid/mime.conf'
};

/**
 * Get current configuration
 */
router.get('/:configType', asyncHandler(async (req, res) => {
  const { configType } = req.params;
  
  if (!CONFIG_PATHS[configType]) {
    throw new NotFoundError('Configuration type not found');
  }

  try {
    const configContent = await fs.readFile(CONFIG_PATHS[configType], 'utf8');
    
    res.json({
      configType,
      content: configContent,
      lastModified: (await fs.stat(CONFIG_PATHS[configType])).mtime,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new NotFoundError('Configuration file not found');
    }
    throw error;
  }
}));

/**
 * Update configuration (admin only)
 */
router.put('/:configType',
  requireAdmin,
  [
    body('content').notEmpty().withMessage('Configuration content is required'),
    body('backup').optional().isBoolean().withMessage('Backup flag must be boolean')
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('Validation failed', errors.array());
    }

    const { configType } = req.params;
    const { content, backup = true } = req.body;
    
    if (!CONFIG_PATHS[configType]) {
      throw new NotFoundError('Configuration type not found');
    }

    const configPath = CONFIG_PATHS[configType];

    try {
      // Create backup if requested
      if (backup && await fs.pathExists(configPath)) {
        const backupPath = `${configPath}.backup.${Date.now()}`;
        await fs.copy(configPath, backupPath);
        console.log(`Configuration backup created: ${backupPath}`);
      }

      // Validate configuration syntax (for squid.conf)
      if (configType === 'squid') {
        await validateSquidConfig(content);
      }

      // Write new configuration
      await fs.writeFile(configPath, content, 'utf8');

      // Broadcast configuration change
      if (global.broadcast) {
        global.broadcast({
          type: 'config_updated',
          configType,
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        message: 'Configuration updated successfully',
        configType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      throw new ValidationError('Failed to update configuration', error.message);
    }
  })
);

/**
 * Get configuration templates
 */
router.get('/templates/:templateType', asyncHandler(async (req, res) => {
  const { templateType } = req.params;
  
  const templates = {
    basic: getBasicSquidTemplate(),
    authenticated: getAuthenticatedSquidTemplate(),
    enterprise: getEnterpriseSquidTemplate()
  };

  if (!templates[templateType]) {
    throw new NotFoundError('Template not found');
  }

  res.json({
    templateType,
    content: templates[templateType],
    description: getTemplateDescription(templateType),
    timestamp: new Date().toISOString()
  });
}));

/**
 * Validate configuration
 */
router.post('/validate/:configType',
  [
    body('content').notEmpty().withMessage('Configuration content is required')
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('Validation failed', errors.array());
    }

    const { configType } = req.params;
    const { content } = req.body;

    try {
      let validationResult = { valid: true, errors: [], warnings: [] };

      if (configType === 'squid') {
        validationResult = await validateSquidConfig(content);
      }

      res.json({
        configType,
        validation: validationResult,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.json({
        configType,
        validation: {
          valid: false,
          errors: [error.message],
          warnings: []
        },
        timestamp: new Date().toISOString()
      });
    }
  })
);

/**
 * Get configuration backups
 */
router.get('/backups/:configType', asyncHandler(async (req, res) => {
  const { configType } = req.params;
  
  if (!CONFIG_PATHS[configType]) {
    throw new NotFoundError('Configuration type not found');
  }

  const configDir = path.dirname(CONFIG_PATHS[configType]);
  const configFile = path.basename(CONFIG_PATHS[configType]);
  
  try {
    const files = await fs.readdir(configDir);
    const backups = files
      .filter(file => file.startsWith(`${configFile}.backup.`))
      .map(async (file) => {
        const filePath = path.join(configDir, file);
        const stats = await fs.stat(filePath);
        return {
          filename: file,
          timestamp: stats.mtime,
          size: stats.size
        };
      });

    const backupList = await Promise.all(backups);
    backupList.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    res.json({
      configType,
      backups: backupList,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    throw new ValidationError('Failed to list backups', error.message);
  }
}));

/**
 * Restore configuration from backup (admin only)
 */
router.post('/restore/:configType/:backupFile',
  requireAdmin,
  asyncHandler(async (req, res) => {
    const { configType, backupFile } = req.params;
    
    if (!CONFIG_PATHS[configType]) {
      throw new NotFoundError('Configuration type not found');
    }

    const configDir = path.dirname(CONFIG_PATHS[configType]);
    const backupPath = path.join(configDir, backupFile);
    const configPath = CONFIG_PATHS[configType];

    try {
      // Verify backup file exists and is valid
      if (!await fs.pathExists(backupPath) || !backupFile.includes('.backup.')) {
        throw new NotFoundError('Backup file not found');
      }

      // Create current backup before restore
      const currentBackupPath = `${configPath}.backup.${Date.now()}`;
      if (await fs.pathExists(configPath)) {
        await fs.copy(configPath, currentBackupPath);
      }

      // Restore from backup
      await fs.copy(backupPath, configPath);

      // Broadcast configuration restore
      if (global.broadcast) {
        global.broadcast({
          type: 'config_restored',
          configType,
          backupFile,
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        message: 'Configuration restored successfully',
        configType,
        backupFile,
        currentBackup: path.basename(currentBackupPath),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      throw new ValidationError('Failed to restore configuration', error.message);
    }
  })
);

/**
 * Helper function to validate Squid configuration
 */
async function validateSquidConfig(content) {
  // Write temporary config file
  const tempConfigPath = `/tmp/squid.conf.temp.${Date.now()}`;
  
  try {
    await fs.writeFile(tempConfigPath, content, 'utf8');
    
    // Use squid to parse and validate the configuration
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    
    const { stdout, stderr } = await execAsync(`squid -k parse -f ${tempConfigPath}`);
    
    // Clean up temp file
    await fs.remove(tempConfigPath);
    
    return {
      valid: true,
      errors: [],
      warnings: stderr ? [stderr] : []
    };
  } catch (error) {
    // Clean up temp file
    try {
      await fs.remove(tempConfigPath);
    } catch (cleanupError) {
      console.warn('Failed to clean up temp config file:', cleanupError);
    }
    
    return {
      valid: false,
      errors: [error.message],
      warnings: []
    };
  }
}

/**
 * Configuration templates
 */
function getBasicSquidTemplate() {
  return `# Basic Squid Configuration
http_port 3128

# Cache settings
cache_dir ufs /var/spool/squid 1024 16 256
cache_mem 256 MB

# Access control
acl localnet src ***********/16
acl localnet src 10.0.0.0/8
acl localnet src **********/12

acl Safe_ports port 80
acl Safe_ports port 443
acl CONNECT method CONNECT

http_access deny !Safe_ports
http_access deny CONNECT !SSL_ports
http_access allow localnet
http_access allow localhost
http_access deny all

# Logging
access_log /var/log/squid/access.log
cache_log /var/log/squid/cache.log`;
}

function getAuthenticatedSquidTemplate() {
  return `# Authenticated Squid Configuration
http_port 3128

# Authentication
auth_param basic program /usr/lib/squid/basic_ncsa_auth /etc/squid/passwd
auth_param basic children 5
auth_param basic realm Squid Proxy
acl authenticated proxy_auth REQUIRED

# Cache settings
cache_dir ufs /var/spool/squid 1024 16 256
cache_mem 256 MB

# Access control
acl localnet src ***********/16
acl Safe_ports port 80
acl Safe_ports port 443
acl CONNECT method CONNECT

http_access deny !Safe_ports
http_access deny CONNECT !SSL_ports
http_access allow authenticated
http_access deny all

# Logging
access_log /var/log/squid/access.log`;
}

function getEnterpriseSquidTemplate() {
  return `# Enterprise Squid Configuration
http_port 3128

# Cache settings
cache_dir ufs /var/spool/squid 2048 16 256
cache_mem 512 MB
maximum_object_size 100 MB

# Performance tuning
dns_nameservers ******* *******
client_lifetime 1 day

# Access control
acl localnet src ***********/16
acl business_hours time MTWHF 09:00-18:00
acl Safe_ports port 80
acl Safe_ports port 443

http_access allow localnet business_hours
http_access deny all

# Security
via off
forwarded_for delete

# Logging
access_log /var/log/squid/access.log squid`;
}

function getTemplateDescription(templateType) {
  const descriptions = {
    basic: 'Basic proxy configuration with minimal settings',
    authenticated: 'Configuration with user authentication required',
    enterprise: 'Advanced configuration with performance tuning and security features'
  };
  return descriptions[templateType] || 'Configuration template';
}

module.exports = router;
