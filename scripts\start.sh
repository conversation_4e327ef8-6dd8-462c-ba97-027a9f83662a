#!/bin/bash

# Squid Proxy Solution - Start Script
set -e

echo "🚀 Starting Squid Proxy Solution..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before continuing!"
    echo "   Default admin credentials: admin/admin123"
    echo ""
fi

# Create necessary directories
echo "📁 Creating data directories..."
mkdir -p data/{cache,logs,database,uploads}
mkdir -p config/{prometheus,grafana}

# Set permissions
echo "🔐 Setting permissions..."
chmod -R 755 data/
chmod +x scripts/*.sh

# Check Docker and Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Build and start services
echo "🏗️  Building and starting services..."
if command -v docker-compose &> /dev/null; then
    docker-compose up -d --build
else
    docker compose up -d --build
fi

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
services=("squid-proxy" "squid-api" "squid-frontend")

for service in "${services[@]}"; do
    if docker ps --filter "name=$service" --filter "status=running" | grep -q "$service"; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
        docker logs "$service" --tail 20
    fi
done

echo ""
echo "🎉 Squid Proxy Solution started successfully!"
echo ""
echo "📊 Access the web interface at: http://localhost:8080"
echo "🔧 Squid proxy is available at: http://localhost:3128"
echo "🔌 API endpoint: http://localhost:3000"
echo ""
echo "👤 Default login credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "⚠️  Please change the default password after first login!"
echo ""
echo "📋 Useful commands:"
echo "   View logs: docker-compose logs -f [service-name]"
echo "   Stop services: docker-compose down"
echo "   Restart services: docker-compose restart"
echo "   Update services: docker-compose pull && docker-compose up -d"
echo ""
