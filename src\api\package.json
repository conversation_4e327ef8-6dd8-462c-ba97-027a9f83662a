{"name": "squid-proxy-api", "version": "1.0.0", "description": "Node.js API for Squid proxy management and monitoring", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["squid", "proxy", "api", "management", "monitoring"], "author": "Squid Proxy Solution", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "ws": "^8.13.0", "tail": "^2.2.6", "fs-extra": "^11.1.1", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.4.0", "sqlite3": "^5.1.6", "sequelize": "^6.32.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.44.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^16.0.1", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}