const { Sequelize } = require('sequelize');
const path = require('path');
const { hashPassword } = require('../middleware/auth');

// Database configuration
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: path.join(__dirname, '../../../data/squid_proxy.db'),
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  define: {
    timestamps: true,
    underscored: false
  }
});

// User model
const User = sequelize.define('User', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50]
    }
  },
  password: {
    type: Sequelize.STRING,
    allowNull: false
  },
  email: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  role: {
    type: Sequelize.ENUM('admin', 'user'),
    defaultValue: 'user'
  },
  isActive: {
    type: Sequelize.BOOLEAN,
    defaultValue: true
  },
  lastLogin: {
    type: Sequelize.DATE,
    allowNull: true
  }
});

// Configuration model for storing configuration history
const Configuration = sequelize.define('Configuration', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  configType: {
    type: Sequelize.STRING,
    allowNull: false
  },
  content: {
    type: Sequelize.TEXT,
    allowNull: false
  },
  version: {
    type: Sequelize.INTEGER,
    defaultValue: 1
  },
  userId: {
    type: Sequelize.INTEGER,
    references: {
      model: User,
      key: 'id'
    }
  },
  isActive: {
    type: Sequelize.BOOLEAN,
    defaultValue: false
  },
  description: {
    type: Sequelize.STRING,
    allowNull: true
  }
});

// Log entry model for storing parsed log entries
const LogEntry = sequelize.define('LogEntry', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  timestamp: {
    type: Sequelize.DATE,
    allowNull: false
  },
  clientIp: {
    type: Sequelize.STRING,
    allowNull: false
  },
  method: {
    type: Sequelize.STRING,
    allowNull: false
  },
  url: {
    type: Sequelize.TEXT,
    allowNull: false
  },
  httpStatus: {
    type: Sequelize.INTEGER,
    allowNull: false
  },
  responseSize: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  userAgent: {
    type: Sequelize.TEXT,
    allowNull: true
  },
  referer: {
    type: Sequelize.TEXT,
    allowNull: true
  },
  processingTime: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  }
});

// Statistics model for storing aggregated statistics
const Statistics = sequelize.define('Statistics', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  date: {
    type: Sequelize.DATEONLY,
    allowNull: false,
    unique: true
  },
  totalRequests: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  totalBytes: {
    type: Sequelize.BIGINT,
    defaultValue: 0
  },
  cacheHits: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  cacheMisses: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  uniqueClients: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  },
  topDomains: {
    type: Sequelize.JSON,
    defaultValue: []
  },
  errorCount: {
    type: Sequelize.INTEGER,
    defaultValue: 0
  }
});

// Define associations
User.hasMany(Configuration, { foreignKey: 'userId' });
Configuration.belongsTo(User, { foreignKey: 'userId' });

// Initialize database
const initializeDatabase = async () => {
  try {
    // Test connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Create tables
    await sequelize.sync({ alter: true });
    console.log('Database tables synchronized.');

    // Create default admin user if it doesn't exist
    await createDefaultAdmin();
    
    console.log('Database initialization completed.');
  } catch (error) {
    console.error('Unable to initialize database:', error);
    throw error;
  }
};

// Create default admin user
const createDefaultAdmin = async () => {
  try {
    const adminExists = await User.findOne({ where: { role: 'admin' } });
    
    if (!adminExists) {
      const defaultPassword = process.env.ADMIN_PASSWORD || 'admin123';
      const hashedPassword = await hashPassword(defaultPassword);
      
      await User.create({
        username: process.env.ADMIN_USERNAME || 'admin',
        password: hashedPassword,
        email: 'admin@localhost',
        role: 'admin',
        isActive: true
      });
      
      console.log('Default admin user created.');
      console.log(`Username: ${process.env.ADMIN_USERNAME || 'admin'}`);
      console.log(`Password: ${defaultPassword}`);
      console.log('Please change the default password after first login!');
    }
  } catch (error) {
    console.error('Error creating default admin user:', error);
  }
};

module.exports = {
  sequelize,
  User,
  Configuration,
  LogEntry,
  Statistics,
  initializeDatabase
};
