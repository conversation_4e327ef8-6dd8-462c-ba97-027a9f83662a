const express = require('express');
const fs = require('fs-extra');
const { Tail } = require('tail');
const { async<PERSON>and<PERSON>, NotFoundError } = require('../middleware/errorHandler');
const { LogEntry } = require('../database/models');

const router = express.Router();

// Log file paths
const LOG_PATHS = {
  access: '/var/log/squid/access.log',
  cache: '/var/log/squid/cache.log',
  store: '/var/log/squid/store.log'
};

/**
 * Get log entries with pagination and filtering
 */
router.get('/:logType', asyncHandler(async (req, res) => {
  const { logType } = req.params;
  const { 
    page = 1, 
    limit = 100, 
    search = '', 
    startDate, 
    endDate,
    clientIp,
    httpStatus 
  } = req.query;

  if (!LOG_PATHS[logType]) {
    throw new NotFoundError('Log type not found');
  }

  try {
    if (logType === 'access') {
      // Get parsed log entries from database
      const where = {};
      
      if (search) {
        where.url = { [require('sequelize').Op.like]: `%${search}%` };
      }
      
      if (startDate && endDate) {
        where.timestamp = {
          [require('sequelize').Op.between]: [new Date(startDate), new Date(endDate)]
        };
      }
      
      if (clientIp) {
        where.clientIp = clientIp;
      }
      
      if (httpStatus) {
        where.httpStatus = httpStatus;
      }

      const offset = (page - 1) * limit;
      const { count, rows } = await LogEntry.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset,
        order: [['timestamp', 'DESC']]
      });

      res.json({
        logType,
        entries: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        },
        timestamp: new Date().toISOString()
      });
    } else {
      // Read raw log file for cache and store logs
      const logContent = await fs.readFile(LOG_PATHS[logType], 'utf8');
      const lines = logContent.split('\n').filter(line => line.trim());
      
      // Apply search filter
      let filteredLines = lines;
      if (search) {
        filteredLines = lines.filter(line => 
          line.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedLines = filteredLines.slice(startIndex, endIndex);

      res.json({
        logType,
        entries: paginatedLines,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredLines.length,
          pages: Math.ceil(filteredLines.length / limit)
        },
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    if (error.code === 'ENOENT') {
      res.json({
        logType,
        entries: [],
        pagination: { page: 1, limit, total: 0, pages: 0 },
        error: 'Log file not found',
        timestamp: new Date().toISOString()
      });
    } else {
      throw error;
    }
  }
}));

/**
 * Get real-time log stream
 */
router.get('/:logType/stream', asyncHandler(async (req, res) => {
  const { logType } = req.params;
  
  if (!LOG_PATHS[logType]) {
    throw new NotFoundError('Log type not found');
  }

  // Set up Server-Sent Events
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*'
  });

  try {
    const tail = new Tail(LOG_PATHS[logType], { follow: true });
    
    tail.on('line', (data) => {
      res.write(`data: ${JSON.stringify({ line: data, timestamp: new Date().toISOString() })}\n\n`);
    });

    tail.on('error', (error) => {
      res.write(`data: ${JSON.stringify({ error: error.message, timestamp: new Date().toISOString() })}\n\n`);
    });

    // Clean up on client disconnect
    req.on('close', () => {
      tail.unwatch();
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({ message: 'Connected to log stream', timestamp: new Date().toISOString() })}\n\n`);
  } catch (error) {
    res.write(`data: ${JSON.stringify({ error: 'Failed to start log stream', timestamp: new Date().toISOString() })}\n\n`);
    res.end();
  }
}));

/**
 * Download log file
 */
router.get('/:logType/download', asyncHandler(async (req, res) => {
  const { logType } = req.params;
  
  if (!LOG_PATHS[logType]) {
    throw new NotFoundError('Log type not found');
  }

  const logPath = LOG_PATHS[logType];
  
  try {
    const stats = await fs.stat(logPath);
    const filename = `${logType}-${new Date().toISOString().split('T')[0]}.log`;
    
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Length', stats.size);
    
    const stream = fs.createReadStream(logPath);
    stream.pipe(res);
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new NotFoundError('Log file not found');
    }
    throw error;
  }
}));

/**
 * Get log statistics
 */
router.get('/:logType/stats', asyncHandler(async (req, res) => {
  const { logType } = req.params;
  const { period = '24h' } = req.query;
  
  if (logType !== 'access') {
    throw new NotFoundError('Statistics only available for access logs');
  }

  try {
    const { Op } = require('sequelize');
    const startDate = getStartDateForPeriod(period);
    
    const stats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('COUNT', '*'), 'totalRequests'],
        [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('clientIp'))), 'uniqueClients'],
        [require('sequelize').fn('SUM', require('sequelize').col('responseSize')), 'totalBytes'],
        [require('sequelize').fn('AVG', require('sequelize').col('processingTime')), 'avgProcessingTime']
      ],
      raw: true
    });

    const statusStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        'httpStatus',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['httpStatus'],
      raw: true
    });

    const topDomains = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('SUBSTR', require('sequelize').col('url'), 1, 100), 'domain'],
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['domain'],
      order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
      limit: 10,
      raw: true
    });

    res.json({
      period,
      stats: stats[0] || {},
      statusDistribution: statusStats,
      topDomains,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting log statistics:', error);
    res.json({
      period,
      stats: {},
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Helper function to get start date for period
 */
function getStartDateForPeriod(period) {
  const now = new Date();
  
  switch (period) {
    case '1h':
      return new Date(now.getTime() - 60 * 60 * 1000);
    case '24h':
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }
}

module.exports = router;
