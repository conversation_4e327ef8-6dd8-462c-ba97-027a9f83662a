{"name": "squid-proxy-frontend", "version": "1.0.0", "description": "React frontend for Squid proxy management", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "axios": "^1.4.0", "recharts": "^2.7.2", "lucide-react": "^0.263.1", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.45.2", "react-query": "^3.39.3", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "framer-motion": "^10.12.18", "react-table": "^7.8.0", "react-syntax-highlighter": "^15.5.0", "monaco-editor": "^0.40.0", "@monaco-editor/react": "^4.5.1"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27"}, "keywords": ["react", "squid", "proxy", "management", "dashboard"], "author": "Squid Proxy Solution", "license": "MIT"}