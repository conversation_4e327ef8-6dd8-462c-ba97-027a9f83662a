const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { Statistics, LogEntry } = require('../database/models');

const router = express.Router();

/**
 * Get dashboard statistics
 */
router.get('/dashboard', asyncHandler(async (req, res) => {
  const { period = '24h' } = req.query;
  
  try {
    const { Op } = require('sequelize');
    const startDate = getStartDateForPeriod(period);
    
    // Get basic statistics
    const basicStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('COUNT', '*'), 'totalRequests'],
        [require('sequelize').fn('COUNT', require('sequelize').fn('DISTINCT', require('sequelize').col('clientIp'))), 'uniqueClients'],
        [require('sequelize').fn('SUM', require('sequelize').col('responseSize')), 'totalBytes'],
        [require('sequelize').fn('AVG', require('sequelize').col('processingTime')), 'avgResponseTime']
      ],
      raw: true
    });

    // Get hourly request distribution
    const hourlyStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('strftime', '%H', require('sequelize').col('timestamp')), 'hour'],
        [require('sequelize').fn('COUNT', '*'), 'requests']
      ],
      group: [require('sequelize').fn('strftime', '%H', require('sequelize').col('timestamp'))],
      order: [[require('sequelize').fn('strftime', '%H', require('sequelize').col('timestamp')), 'ASC']],
      raw: true
    });

    // Get status code distribution
    const statusStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        'httpStatus',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['httpStatus'],
      order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
      raw: true
    });

    // Get top domains
    const topDomains = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('SUBSTR', require('sequelize').col('url'), 8, 50), 'domain'],
        [require('sequelize').fn('COUNT', '*'), 'requests'],
        [require('sequelize').fn('SUM', require('sequelize').col('responseSize')), 'bytes']
      ],
      group: [require('sequelize').fn('SUBSTR', require('sequelize').col('url'), 8, 50)],
      order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
      limit: 10,
      raw: true
    });

    // Get top clients
    const topClients = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        'clientIp',
        [require('sequelize').fn('COUNT', '*'), 'requests'],
        [require('sequelize').fn('SUM', require('sequelize').col('responseSize')), 'bytes']
      ],
      group: ['clientIp'],
      order: [[require('sequelize').fn('COUNT', '*'), 'DESC']],
      limit: 10,
      raw: true
    });

    res.json({
      period,
      summary: basicStats[0] || {
        totalRequests: 0,
        uniqueClients: 0,
        totalBytes: 0,
        avgResponseTime: 0
      },
      hourlyDistribution: hourlyStats,
      statusDistribution: statusStats,
      topDomains,
      topClients,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting dashboard statistics:', error);
    res.json({
      period,
      summary: {},
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Get bandwidth statistics
 */
router.get('/bandwidth', asyncHandler(async (req, res) => {
  const { period = '24h', interval = 'hour' } = req.query;
  
  try {
    const { Op } = require('sequelize');
    const startDate = getStartDateForPeriod(period);
    
    let timeFormat;
    switch (interval) {
      case 'minute':
        timeFormat = '%Y-%m-%d %H:%M';
        break;
      case 'hour':
        timeFormat = '%Y-%m-%d %H';
        break;
      case 'day':
        timeFormat = '%Y-%m-%d';
        break;
      default:
        timeFormat = '%Y-%m-%d %H';
    }

    const bandwidthStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('strftime', timeFormat, require('sequelize').col('timestamp')), 'timeSlot'],
        [require('sequelize').fn('SUM', require('sequelize').col('responseSize')), 'totalBytes'],
        [require('sequelize').fn('COUNT', '*'), 'totalRequests']
      ],
      group: [require('sequelize').fn('strftime', timeFormat, require('sequelize').col('timestamp'))],
      order: [[require('sequelize').fn('strftime', timeFormat, require('sequelize').col('timestamp')), 'ASC']],
      raw: true
    });

    res.json({
      period,
      interval,
      data: bandwidthStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting bandwidth statistics:', error);
    res.json({
      period,
      interval,
      data: [],
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Get cache statistics
 */
router.get('/cache', asyncHandler(async (req, res) => {
  const { period = '24h' } = req.query;
  
  try {
    // This would require parsing cache log or using squid cache manager
    // For now, return mock data
    const cacheStats = {
      hitRate: 65.5,
      missRate: 34.5,
      totalObjects: 15420,
      cacheSize: '1.2 GB',
      maxCacheSize: '2.0 GB',
      cacheUtilization: 60.0
    };

    res.json({
      period,
      stats: cacheStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting cache statistics:', error);
    res.json({
      period,
      stats: {},
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Get performance metrics
 */
router.get('/performance', asyncHandler(async (req, res) => {
  const { period = '24h' } = req.query;
  
  try {
    const { Op } = require('sequelize');
    const startDate = getStartDateForPeriod(period);
    
    const performanceStats = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('AVG', require('sequelize').col('processingTime')), 'avgResponseTime'],
        [require('sequelize').fn('MIN', require('sequelize').col('processingTime')), 'minResponseTime'],
        [require('sequelize').fn('MAX', require('sequelize').col('processingTime')), 'maxResponseTime'],
        [require('sequelize').fn('COUNT', require('sequelize').literal('CASE WHEN processingTime > 5000 THEN 1 END')), 'slowRequests']
      ],
      raw: true
    });

    // Get response time distribution over time
    const timeDistribution = await LogEntry.findAll({
      where: {
        timestamp: {
          [Op.gte]: startDate
        }
      },
      attributes: [
        [require('sequelize').fn('strftime', '%Y-%m-%d %H', require('sequelize').col('timestamp')), 'hour'],
        [require('sequelize').fn('AVG', require('sequelize').col('processingTime')), 'avgResponseTime']
      ],
      group: [require('sequelize').fn('strftime', '%Y-%m-%d %H', require('sequelize').col('timestamp'))],
      order: [[require('sequelize').fn('strftime', '%Y-%m-%d %H', require('sequelize').col('timestamp')), 'ASC']],
      raw: true
    });

    res.json({
      period,
      summary: performanceStats[0] || {},
      timeDistribution,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting performance statistics:', error);
    res.json({
      period,
      summary: {},
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}));

/**
 * Helper function to get start date for period
 */
function getStartDateForPeriod(period) {
  const now = new Date();
  
  switch (period) {
    case '1h':
      return new Date(now.getTime() - 60 * 60 * 1000);
    case '24h':
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    case '7d':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }
}

module.exports = router;
