#!/bin/bash

# Squid Proxy Entrypoint Script
set -e

# Environment variables with defaults
SQUID_USER=${SQUID_USER:-proxy}
SQUID_CACHE_DIR=${SQUID_CACHE_DIR:-/var/spool/squid}
SQUID_LOG_DIR=${SQUID_LOG_DIR:-/var/log/squid}
SQUID_CONFIG_DIR=${SQUID_CONFIG_DIR:-/etc/squid}

echo "Starting Squid Proxy Server..."
echo "Squid User: $SQUID_USER"
echo "Cache Directory: $SQUID_CACHE_DIR"
echo "Log Directory: $SQUID_LOG_DIR"
echo "Config Directory: $SQUID_CONFIG_DIR"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Create necessary directories
log "Creating directories..."
mkdir -p $SQUID_CACHE_DIR $SQUID_LOG_DIR
chown -R $SQUID_USER:$SQUID_USER $SQUID_CACHE_DIR $SQUID_LOG_DIR

# Initialize cache directories if they don't exist
if [ ! -d "$SQUID_CACHE_DIR/00" ]; then
    log "Initializing Squid cache directories..."
    squid -z -f $SQUID_CONFIG_DIR/squid.conf
    chown -R $SQUID_USER:$SQUID_USER $SQUID_CACHE_DIR
fi

# Test configuration
log "Testing Squid configuration..."
if ! squid -k parse -f $SQUID_CONFIG_DIR/squid.conf; then
    log "ERROR: Squid configuration test failed!"
    exit 1
fi

# Set up log rotation
log "Setting up log rotation..."
service rsyslog start
service cron start

# Create authentication file if it doesn't exist
if [ ! -f "$SQUID_CONFIG_DIR/passwd" ] && [ -n "$SQUID_AUTH_USER" ] && [ -n "$SQUID_AUTH_PASS" ]; then
    log "Creating authentication file..."
    htpasswd -cb $SQUID_CONFIG_DIR/passwd $SQUID_AUTH_USER $SQUID_AUTH_PASS
    chown $SQUID_USER:$SQUID_USER $SQUID_CONFIG_DIR/passwd
    chmod 640 $SQUID_CONFIG_DIR/passwd
fi

# Handle signals for graceful shutdown
trap 'log "Received SIGTERM, shutting down gracefully..."; squid -k shutdown; exit 0' TERM
trap 'log "Received SIGINT, shutting down gracefully..."; squid -k shutdown; exit 0' INT

log "Squid Proxy Server initialization complete."

# Execute the main command
exec "$@"
