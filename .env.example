# Squid Proxy Configuration
SQUID_PORT=3128
SQUID_HTTP_ACCESS=allow all
SQUID_CACHE_SIZE=1024
SQUID_CACHE_DIR=/var/spool/squid

# Web UI Configuration
WEB_UI_PORT=8080
API_PORT=3000

# Database Configuration (for user management)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=squid_proxy
DB_USER=squid_user
DB_PASSWORD=change_me_in_production

# Authentication
JWT_SECRET=your_jwt_secret_here_change_in_production
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Logging
LOG_LEVEL=info
SQUID_LOG_LEVEL=1

# Security
CORS_ORIGIN=http://localhost:8080
SESSION_SECRET=your_session_secret_here

# Network Configuration
NETWORK_SUBNET=**********/16
SQUID_CONTAINER_IP=***********
API_CONTAINER_IP=***********
FRONTEND_CONTAINER_IP=***********

# Volume Paths
SQUID_CONFIG_PATH=./config/squid
SQUID_CACHE_PATH=./data/cache
SQUID_LOGS_PATH=./data/logs
