# Squid Authentication Configuration
# This file contains authentication-specific settings

# Basic Authentication Configuration
auth_param basic program /usr/lib/squid/basic_ncsa_auth /etc/squid/passwd
auth_param basic children 5 startup=5 idle=1
auth_param basic realm "Squid Proxy Server - Authentication Required"
auth_param basic credentialsttl 2 hours
auth_param basic casesensitive off

# Authentication ACLs
acl authenticated proxy_auth REQUIRED
acl auth_users proxy_auth "/etc/squid/users.txt"

# Time-based authentication
acl auth_business_hours time MTWHF 09:00-18:00
acl auth_weekend time SA SU

# IP-based authentication bypass (for trusted networks)
acl trusted_network src ***********/24
acl admin_network src *************/24

# Authentication rules (to be included in main squid.conf)
# Uncomment these lines in squid.conf to enable authentication:
#
# # Allow admin network without authentication
# http_access allow admin_network
# 
# # Allow trusted network during business hours without auth
# http_access allow trusted_network auth_business_hours
# 
# # Require authentication for all other access
# http_access allow authenticated
# 
# # Deny unauthenticated access
# http_access deny all

# External authentication helpers (examples)
# LDAP Authentication
# auth_param basic program /usr/lib/squid/basic_ldap_auth -R -b "dc=example,dc=com" -D "cn=squid,dc=example,dc=com" -w "password" -f sAMAccountName=%s -h ldap.example.com

# Active Directory Authentication
# auth_param basic program /usr/lib/squid/basic_ldap_auth -R -b "dc=example,dc=com" -D "<EMAIL>" -w "password" -f sAMAccountName=%s -h ad.example.com

# Database Authentication (MySQL)
# auth_param basic program /usr/lib/squid/basic_db_auth --dsn DBI:mysql:database=squid;host=localhost --user squid --password password --table users --usercol username --passcol password

# Digest Authentication (more secure than basic)
# auth_param digest program /usr/lib/squid/digest_file_auth -c /etc/squid/digest_passwd
# auth_param digest children 5 startup=5 idle=1
# auth_param digest realm "Squid Proxy Server"
# auth_param digest nonce_garbage_interval 5 minutes
# auth_param digest nonce_max_duration 30 minutes
# auth_param digest nonce_max_count 50
